export interface DeliveredPackage {
    createTime: string
    express: {
        expressCompanyCode: string
        expressCompanyName: string
        expressNo: string
    }
    id: string
    receiver: {
        address: string
        mobile: string
        name: string
    }
    status:
        | 'COMPLETED'
        | 'WAITING_FOR_PUTIN'
        | 'WAITING_FOR_RECEIVE'
        | 'WAITING_FOR_DELIVER'
        | 'FINISHED'
        | 'CLOSED'
        | 'SELLER_CLOSED'
        | 'BUYER_CLOSED'
        | 'SYSTEM_CLOSED'
        | 'PAID'
        | 'UNPAID'
        | 'AUDIT_FAIL_CLOSED'
        | 'PAYMENT_AUDIT'
        | 'SUPPLIER_DISABLE'
        | 'SUPPLIER_SELL_OFF'
        | 'UNUSABLE'
        | 'PLATFORM_SELL_OFF'
        | 'SELL_OUT'
        | 'SELL_ON'
        | 'SELL_OFF'
        | 'UNDER_REVIEW'
    packageStatus: 'WAITING_FOR_DELIVER' | 'WAITING_FOR_RECEIVE' | 'COMPLETED'
    type: 'PRINT_EXPRESS' | 'EXPRESS' | 'WITHOUT'
    orderItems: OrderItem[]
}

interface OrderItem {
    id: string
    image: string
    num: number
    productId: string
    productName: string
    specs: string[]
}
