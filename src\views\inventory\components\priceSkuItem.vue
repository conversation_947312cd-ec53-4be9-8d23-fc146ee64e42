<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-06-14 20:28:23
 * @LastEditors: lexy
 * @LastEditTime: 2024-04-03 09:28:11
-->
<template>
    <div class="item">
        <el-row :gutter="20" justify="space-around" style="background-color: #eee; height: 40px; line-height: 40px; margin-left: 0px; margin-right: 0px">
            <el-col :span="12">规格</el-col>
            <el-col :span="5">划线价</el-col>
            <el-col :span="5">供货价</el-col>
        </el-row>
        <el-scrollbar height="400px">
            <el-row v-for="item in itemSku" :key="item.id" :gutter="20" justify="space-around" style="margin-left: 0px; margin-right: 0px">
                <el-col :span="12">{{ item.specs?.join(' ') || '单规格商品' }}</el-col>
                <el-col :span="5">
                    <decimal-input v-model="item.price" :decimal-places="2" :max="9999999999" :min="0" style="width: 110px">
                        <template #prepend>￥</template>
                    </decimal-input>
                </el-col>
                <el-col :span="5">
                    <decimal-input v-model="item.salePrice" :decimal-places="2" :max="9999999999" :min="0" style="width: 110px">
                        <template #prepend>￥</template>
                    </decimal-input>
                </el-col>
            </el-row>
        </el-scrollbar>
    </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue'
import type { CommoditySpecTable } from '@/views/goods/types/index'
import DecimalInput from '@/components/decimal-input/decimal-input.vue'
const props = defineProps({
    skus: {
        type: Array as PropType<CommoditySpecTable[]>,
        default: () => [],
    },
})
const itemSku = ref(props.skus)
const setALLskyPrice = (key: 'price' | 'salePrice', price: string | number) => {
    itemSku.value.forEach((item: CommoditySpecTable) => {
        item[key] = price
    })
}
defineExpose({
    setALLskyPrice,
    itemSku,
})
</script>

<style lang="scss" scoped>
.item {
    margin-top: 25px;

    .el-col {
        text-align: center;
    }
}

.name {
    width: 80px;
    text-align: center;
}

.el-row {
    margin-bottom: 8px;
}
</style>
