<script setup lang="ts">
import { ref, reactive } from 'vue'
import { doGetEvaluate, doPutIsExcellentEvaluate, doPostShopReplyEvaluate } from '@/apis/order/appraise'
import { ElMessage, ElMessageBox } from 'element-plus'
import SearchForm from '@/views/order/appraise/components/search-form.vue'
import PageManage from '@/components/pageManage/PageManage.vue'
import AppraiseDetails from './components/appraise-details.vue'
import type { Evaluate, EvaluateSearchParams } from '@/views/order/appraise/types'
/*
 *variable
 */
const dialogVisible = ref(false)
const PageConfig = ref({ size: 10, current: 1, total: 0 })
const EvaluateInfoList = ref<Evaluate[][]>([])
const currentId = ref('')
const shopReply = ref('')
const searchCardUp = ref(false)
enum SellTypeEnum {
    CONSIGNMENT = '代销商品',
    PURCHASE = '采购商品',
    OWN = '自有商品',
}
const currentRow = ref<any>({})
const showDetailsDialog = ref(false)
/*
 *lifeCircle
 */
initEvaluateInfo()
/*
 *function
 */
async function initEvaluateInfo(params?: EvaluateSearchParams) {
    const { code, data } = await doGetEvaluate(Object.assign(PageConfig.value, params))
    if (code !== 200) return ElMessage.error('获取评级列表失败')
    PageConfig.value.total = data.total
    PageConfig.value.size = data.size
    EvaluateInfoList.value = processEvaluateData(data.records)
}
/**
 * @LastEditors: lexy
 * @description: 处理 评估 数据
 * @param {*} data
 * @returns {*}
 */
const processEvaluateData = (evaluates: Evaluate[]) => {
    const arr = []
    const map = new Map()
    for (const evaluate of evaluates) {
        const currentEvaluateIndex = map.get(evaluate.packageId)
        if (!currentEvaluateIndex) {
            map.set(evaluate.packageId, arr.length)
            arr[arr.length] = [evaluate]
            continue
        }
        arr[currentEvaluateIndex].push(evaluate)
    }
    return arr
}
// tab表格
/**
 * @LastEditors: lexy
 * @description: 精选
 * @param {*} row
 * @returns {*}
 */
const handleChoiceness = async (row: Evaluate) => {
    const isExcellent = !row.isExcellent
    const { code } = await doPutIsExcellentEvaluate(isExcellent, [row.id])
    if (code !== 200) return ElMessage.error(`${!isExcellent ? '设为' : '取消'}隐藏失败`)
    ElMessage.success(`${!isExcellent ? '设为' : '取消'}隐藏成功`)
    initEvaluateInfo()
}
const handlerReply = (row: Evaluate) => {
    currentId.value = row.id
    dialogVisible.value = true
}
// 商品名称
// 买家昵称
// 成交时间
//  * 评价星级
const searchData = reactive({
    name: '',
    nickname: '',
    clinchTime: '',
    rate: '',
    memberType: '',
})

/**
 * @LastEditors: lexy
 * @description: 处理搜索
 * @returns {*}
 */
const handleSearch = () => {
    const { name, nickname, clinchTime, rate, memberType } = searchData

    const params = { name, nickname, rate, startTime: '', endTime: '', memberType: memberType ? memberType.join(',') : '' } //zrb:解决未选择时join报错无法查询问题
    if (Array.isArray(clinchTime)) {
        params.startTime = clinchTime[0]
        params.endTime = clinchTime[1]
    }
    if (Number(params.rate) === 0) {
        params.rate = ''
    }
    initEvaluateInfo(params)
}
const handleClose = () => {
    ElMessageBox.confirm('取消回复内容将不保存，确定取消吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            shopReply.value = ''
            dialogVisible.value = false
        })
        .catch(() => {})
}
/**
 * @LastEditors: lexy
 * @description: 回复提交
 * @returns {*}
 */
const handleSubmit = async () => {
    if (!shopReply.value.length) return ElMessage.error(`请输入内容`)
    const { code } = await doPostShopReplyEvaluate(currentId.value, shopReply.value)
    if (code !== 200) return ElMessage.error(`回复失败`)
    ElMessage.success(`回复成功`)
    shopReply.value = ''
    dialogVisible.value = false
    initEvaluateInfo()
}
const arraySpanMethod = ({ row, column, rowIndex, columnIndex }: { row: any; column: any; rowIndex: number; columnIndex: number }) => {
    return columnIndex === 0 ? [1, 6] : [0, 0]
}
const handleSelectionChange = (e) => {
    console.log('e', e)
}

const showAppraiseDetails = (rowData: any) => {
    currentRow.value = rowData
    showDetailsDialog.value = true
}
</script>

<template>
    <search-form :search-data="searchData" @click="handleSearch" @show-search-card-change="searchCardUp = $event" />
    <el-table
        :data="EvaluateInfoList"
        :span-method="arraySpanMethod"
        class="table"
        :class="{ searchCardUp: searchCardUp }"
        header-cell-class-name="cellheaderstyle"
        header-row-class-name="rowheaderstyle"
        row-class-name="rowstyle"
        cell-class-name="cellstyle"
    >
        <el-table-column prop="id" align="center">
            <template #header>
                <span class="label">商品</span>
            </template>
            <template #default="{ row: orderRow }">
                <div style="text-align: left; background: #fff" class="theHead">
                    <span>订单号：{{ orderRow[0].orderNo }}</span
                    >&nbsp;&nbsp;&nbsp;<span>下单时间：{{ orderRow[0]?.orderCreateTime }}</span>
                </div>
                <el-table :data="orderRow" style="width: 100%" border :show-header="false" row-class-name="rowstyles" @selection-change="handleSelectionChange">
                    <el-table-column>
                        <template #default="{ row }">
                            <div class="commodity">
                                <el-image :src="row?.image" style="width: 80px; height: 80px; flex-shrink: 0" />
                                <div class="commodity__info">
                                    <p class="commodity__info--name">{{ row?.name }}</p>
                                    <p class="commodity__info--spec">{{ row?.specs?.join(',') }}</p>
                                    <p class="commodity__info--type">{{ SellTypeEnum[row?.sellType as keyof typeof SellTypeEnum] }}</p>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column>
                        <template #default="{ row }">
                            <div class="user__info">
                                <el-image :src="row?.avatar" style="width: 20px; height: 20px; flex-shrink: 0" />
                                <span>
                                    {{ row.nickname || `用户${row.userId.slice(-6)}` }}
                                    <template v-if="row.memberInfos">
                                        <label v-for="item in row.memberInfos" :key="item.id" class="user__info--label" :style="{ color: item.fontColor, border: `1px solid ${item.labelColor}` }">{{
                                            item.name
                                        }}</label>
                                    </template>
                                </span>
                                <el-rate :model-value="row?.rate" disabled text-color="#ff9900" style="flex-shrink: 0" />
                            </div>
                            <span>评论时间：{{ row?.createTime }}</span>
                            <div class="user__comment">
                                <p>{{ row.comment }}</p>
                                <div v-if="row?.shopReply" style="width: 180px; margin-top: 5px" class="shopReply">商家回复：{{ row.shopReply }}</div>
                                <div v-if="row?.platformReply" style="width: 180px; margin-top: 5px" class="shopReply">平台回复：{{ row.platformReply }}</div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" width="150">
                        <template #default="{ row }">
                            <el-row v-if="row?.medias?.length" justify="center">
                                <el-button type="primary" link size="small" @click="showAppraiseDetails(row)">图片视频</el-button>
                            </el-row>
                            <!-- <el-row justify="center" style="margin-top: 15px">
                                <el-button size="small" type="primary" link @click="handleChoiceness(scope.row)">
                                    {{ !scope.row.isExcellent ? '取消隐藏' : '设为隐藏' }}
                                </el-button>
                            </el-row>
                            <el-row justify="center" style="margin-top: 15px">
                                <el-button v-if="!scope.row.platformReply" size="small" type="primary" link @click="handlerReply(scope.row)"> 回复 </el-button>
                            </el-row> -->
                        </template>
                    </el-table-column>
                </el-table>
            </template>
        </el-table-column>
        <el-table-column align="center">
            <template #header>
                <span class="label">评价信息</span>
            </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
            <template #header>
                <span class="label">操作</span>
            </template>
        </el-table-column>
    </el-table>
    <!-- tab表格部分e -->
    <el-row justify="end" align="middle">
        <!-- <el-button type="primary" plain round>批量备注</el-button> -->
        <page-manage v-model="PageConfig" :total="PageConfig.total" @reload="initEvaluateInfo" />
    </el-row>
    <el-dialog v-model="dialogVisible" title="平台回复" width="30%" :align-center="true">
        <el-input v-model="shopReply" :rows="2" type="textarea" placeholder="" maxlength="100" min="2" :clearable="true" />
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleSubmit">确定</el-button>
            </span>
        </template>
    </el-dialog>
    <el-dialog v-model="showDetailsDialog" title="图片视频" width="60%" :align-center="true">
        <appraise-details :details-info="currentRow" />
    </el-dialog>
</template>
<style>
.rowstyle {
    background: #eef1f6 !important;
}
.rowstyles {
    background: #fff !important;
}

.cellstyle {
    border-bottom: 0 !important;
}
.cellstyle:hover {
    background: #eef1f6 !important;
}
.cellheaderstyle {
    font-size: 14px;
    font-weight: normal;
    color: #586884;
    background: #eef1f6 !important;
}
.rowheaderstyle {
    padding: 0 !important;
}
.cellheaderstyle:nth-child(1) > .cell:nth-child(1) {
    padding-left: 10px;
}
.cellheaderstyle:last-child > .cell:last-child {
    padding-right: 10px;
}
.cellheaderstyle > .cell {
    padding: 0;
    height: auto;
}
</style>
<style lang="scss" scoped>
@include b(label) {
    display: block;
    padding: 8px;
    background: #fff;
}
@include b(table) {
    transition: height 0.5s;
    height: calc(100vh - 230px);
}
@include b(flex) {
    @include flex;
    min-height: 68px;
}
@include b(shopReply) {
    width: 90px;
    padding: 10px 5px;
    background: #f3f3f3;
    border-radius: 5px;
    text-align: LEFT;
    color: #333333;
}
@include b(btns) {
    @include flex;
}
:deep(.el-switch__core) {
    height: 30px;
    border-radius: 15px;
    .el-switch__action {
        width: 25px;
        height: 25px;
    }
}
// title样式e
// form样式s
@include b(form) {
    width: 100%;
    background: #f9f9f9;
    margin-bottom: 10px;
    position: relative;
    :deep(.el-form-item) {
        width: 440px;
    }

    :deep(.el-icon) {
        margin-left: 10px;
    }
    @include b(form-flex) {
        position: relative;
        display: flex;
        flex: 3;
        justify-content: space-between;
        flex-wrap: wrap;
        font-size: 14px;
        color: #797979;
    }
    @include e(form-order) {
        margin-left: 15px;
    }
    @include e(from_btn) {
        margin-left: 60px;
    }
}
@include b(from_btn) {
    margin-left: 70px;
}
// form样式e
:deep(.body--header) {
    font-size: 12px;
    color: #000000;
}
.avatar_text {
    cursor: pointer;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1; /* 可以显示的行数，超出部分用...表示*/
    -webkit-box-orient: vertical;
}
@include b(rate_size) {
    :deep(.el-rate__icon) {
        //评分图标大小
        font-size: 25px;
    }
}
@include b(theHead) {
    border-radius: 15px;
    padding: 10px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    /* border: 1px solid #d8eaf9; */
}
@include b(searchCardUp) {
    height: calc(100vh - 400px);
}

@include b(commodity) {
    @include flex(flex-start);
    @include e(info) {
        flex: 1;
        margin-left: 8px;
        @include m(name) {
            font-weight: bold;
            font-size: 14px;
            @include utils-ellipsis(2);
        }
        @include m(spec) {
            font-size: 12px;
            margin-top: 3px;
        }
        @include m(type) {
            margin-top: 5px;
            font-size: 12px;
        }
    }
}
@include b(user) {
    @include e(info) {
        @include flex(space-between);
        span {
            flex: 1;
            margin: 0 10px;
            @include utils-ellipsis(1);
        }
        @include m(label) {
            color: #4589f9;
            font-size: 12px;
            height: 16px;
            line-height: 16px;
            padding: 0 4px;
            border-radius: 2px;
            border: 1px solid #4589f9;
            text-align: center;
            margin-right: 9px;
        }
        @include e(comment) {
            @include utils-ellipsis(4);
            min-height: 95px;
            font-size: 12px;
        }
    }
}
</style>
