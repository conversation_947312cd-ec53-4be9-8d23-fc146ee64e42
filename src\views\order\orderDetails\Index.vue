<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-07-23 16:33:17
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-31 14:17:05
-->
<script setup lang="ts">
import { ref, reactive, defineAsyncComponent, computed } from 'vue'
import { useRoute } from 'vue-router'
import { doGetOrderList } from '@/apis/order'
import { useShopInfoStore } from '@/store/modules/shopInfo'
import type { ApiOrder } from '@/views/order/types/order'
type ActiveName = 'orderInfo' | 'logisticsInfo'
const $shopInfoStore = useShopInfoStore()
/*
 *variable
 */
// 如果订单没有发货 物流信息页面不可查看隐藏
const tabPaneOrderDetails = ref([{ label: '订单信息', name: 'orderInfo' }])

const $route = useRoute()
const isShipment = computed(() => $route.query.packageId)
const reactiveAsyncComponent = reactive({
    orderInfo: defineAsyncComponent(() => import('./components/orderInfo.vue')),
    logisticsInfo: defineAsyncComponent(() => import('./components/logisticsInfo.vue')),
})
const activeName = ref<ActiveName>('orderInfo')
// 订单详情数据
const orderDetailsData = ref<ApiOrder>()
/*
 *lifeCircle
 */
initOrderDetails()
watch(
    () => isShipment.value,
    (res) => {
        if (res) {
            //如果订单已发货 展示物流信息页面
            tabPaneOrderDetails.value.push({ label: '物流信息', name: 'logisticsInfo' })
        }
    },
    {
        immediate: true,
    },
)
/*
 *function
 */
/**
 * @LastEditors: lexy
 * @description: 获取订单详情信息 如果已经发货携带包裹id
 * @returns {*}
 */
async function initOrderDetails() {
    if ($route.query.orderNo) {
        const { code, data } = await doGetOrderList({ usePackage: false, shopId: $route.query.shopId }, $route.query.orderNo as string)
        if (code !== 200) return
        orderDetailsData.value = data
    }
}
</script>

<template>
    <el-tabs v-model="activeName">
        <el-tab-pane v-for="tabPaneItem in tabPaneOrderDetails" :key="tabPaneItem.label" :label="tabPaneItem.label" :name="tabPaneItem.name" />
    </el-tabs>
    <keep-alive>
        <component :is="reactiveAsyncComponent[activeName]" :order="orderDetailsData" />
    </keep-alive>
</template>
