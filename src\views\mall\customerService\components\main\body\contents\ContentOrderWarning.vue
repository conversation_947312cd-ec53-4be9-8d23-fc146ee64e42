<!--
 * @description: 订单预警消息显示组件
 * @Author: AI Assistant
 * @Date: 2025-01-27
-->
<template>
    <div class="message-content-order-warning" :class="clazz">
        <div class="message-content-direction" :class="clazz"></div>
        <div class="order-warning-card">
            <div class="warning-header">
                <el-icon class="warning-icon" color="#E6A23C"><Warning /></el-icon>
                <span class="warning-title">订单预警</span>
                <el-tag :type="getWarningType(warningData.noticeType)" size="small">
                    {{ getWarningText(warningData.noticeType) }}
                </el-tag>
            </div>

            <div class="warning-content">
                <div class="warning-item">
                    <span class="label">商品名称：</span>
                    <span class="value">{{ warningData.productName || '未知商品' }}</span>
                </div>

                <div v-if="warningData.productSpecs && warningData.productSpecs.length > 0" class="warning-item">
                    <span class="label">商品规格：</span>
                    <span class="value specs-value">
                        <el-tag v-for="(spec, index) in warningData.productSpecs" :key="index" size="small" type="info" class="spec-tag">
                            {{ spec }}
                        </el-tag>
                    </span>
                </div>

                <div v-if="warningData.productNum !== undefined" class="warning-item">
                    <span class="label">商品数量：</span>
                    <span class="value">{{ warningData.productNum }}</span>
                </div>

                <div class="warning-item">
                    <span class="label">预警类型：</span>
                    <span class="value">{{ getWarningText(warningData.noticeType) }}</span>
                </div>
            </div>

            <div class="warning-footer">
                <el-button type="primary" size="small" @click="handleViewDetails"> 立即处理 </el-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, PropType } from 'vue'
import { MessageAndShopAdmin } from '@/views/mall/customerService/types'
import { Warning } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

/**
 * msg 消息内容
 * isMine 是否是我的消息
 */
const router = useRouter()
const props = defineProps({
    message: {
        type: Object as PropType<MessageAndShopAdmin>,
        required: true,
    },
    isMine: {
        type: Boolean,
        default: false,
    },
})

const clazz = computed(() => (props.isMine ? 'mine' : 'other'))

// 解析订单预警消息内容
const warningData = computed(() => {
    const messageContent = props.message.message
    if (!messageContent) {
        return {
            productName: '未知商品',
            productNum: 0,
            productSpecs: [],
            noticeType: 'UNKNOWN',
        }
    }

    try {
        const parsed = JSON.parse(messageContent)
        return {
            productName: parsed.productName || '未知商品',
            productNum: parsed.productNum || parsed.num || 0,
            productSpecs: parsed.productSpecs || parsed.specs || [],
            noticeType: parsed.noticeType || 'UNKNOWN',
            orderNo: parsed.orderNo,
            orderTime: parsed.orderTime,
            ...parsed,
        }
    } catch (error) {
        console.error('解析订单预警消息失败:', error)
        return {
            productName: '解析失败',
            productNum: 0,
            productSpecs: [],
            noticeType: 'UNKNOWN',
        }
    }
})

// 获取预警类型对应的标签类型
const getWarningType = (noticeType: string) => {
    const typeMap: Record<string, string> = {
        PENDING_SHIPMENT: 'warning',
        PENDING_PICKUP: 'info',
        UNKNOWN: 'info',
    }
    return typeMap[noticeType] || 'info'
}

// 获取预警类型对应的文本
const getWarningText = (noticeType: string) => {
    const textMap: Record<string, string> = {
        PENDING_SHIPMENT: '待发货',
        PENDING_PICKUP: '待揽件',
        UNKNOWN: '未知类型',
    }
    return textMap[noticeType] || noticeType
}

// 处理立即处理点击事件
const handleViewDetails = () => {
    // 跳转到订单预警页面，并传递相关参数进行筛选
    const query: any = {
        // 如果有订单号，传递订单号进行筛选
        ...(warningData.value.orderNo && { orderNo: warningData.value.orderNo }),
        // 如果有预警类型，传递预警类型进行筛选
        ...(warningData.value.noticeType && { noticeType: warningData.value.noticeType }),
        // 如果有商品名称，传递商品名称进行筛选
        ...(warningData.value.productName && { productName: warningData.value.productName }),
    }

    router.push({
        path: '/order/warning',
        query,
    })
}
</script>

<style scoped lang="scss">
.message-content-order-warning {
    margin: 8px;
    max-width: 320px;

    &.mine {
        .order-warning-card {
            background: linear-gradient(135deg, #dcf2ff 0%, #e8f4ff 100%);
            border-color: #409eff;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
        }
        .message-content-direction {
            border-left-color: #dcf2ff;
        }
    }

    &.other {
        .order-warning-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-color: #e4e7ed;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        .message-content-direction {
            border-right-color: #ffffff;
        }
    }
}

.message-content-direction {
    position: absolute;
    width: 0;
    height: 0;
    border: 8px solid transparent;

    &.mine {
        right: -16px;
        top: 12px;
        border-left: 8px solid #dcf2ff;
    }

    &.other {
        left: -16px;
        top: 12px;
        border-right: 8px solid #ffffff;
    }
}

.order-warning-card {
    position: relative;
    border: 1px solid #e4e7ed;
    border-radius: 12px;
    padding: 16px;
    background: #ffffff;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
    }
}

.warning-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f2f5;

    .warning-icon {
        font-size: 18px;
        padding: 6px;
        background: rgba(230, 162, 60, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .warning-title {
        font-weight: 600;
        color: #1f2329;
        font-size: 15px;
        flex: 1;
    }
}

.warning-content {
    margin-bottom: 16px;

    .warning-item {
        display: flex;
        margin-bottom: 10px;
        font-size: 14px;
        align-items: flex-start;

        &:last-child {
            margin-bottom: 0;
        }

        .label {
            color: #646a73;
            min-width: 80px;
            font-weight: 500;
            flex-shrink: 0;
        }

        .value {
            color: #1f2329;
            flex: 1;
            word-break: break-all;
            font-weight: 400;
            line-height: 1.4;

            &.specs-value {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
                align-items: center;
            }
        }

        .spec-tag {
            margin: 0;
            border-radius: 4px;
            font-size: 12px;
            padding: 2px 6px;
            background: #f0f2f5;
            color: #646a73;
            border: none;
        }
    }
}

.warning-footer {
    text-align: right;
    padding-top: 8px;

    .el-button {
        border-radius: 8px;
        padding: 8px 16px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }
    }
}
</style>
