<!--
   * @description: 管理员设置
   * @Author: lexy
  -->
<template>
    <el-button type="primary" round @click="newAdmin">新增管理员</el-button>
    <el-table
        :data="adminPage.records"
        style="margin-top: 10px"
        :header-row-style="{ fontSize: '12px', color: '#909399' }"
        :header-cell-style="{ background: '#f6f8fa' }"
        :cell-style="{ fontSize: '12px', color: '#333333' }"
    >
        <el-table-column align="center" label="角色名称" prop="userRole.role.name"></el-table-column>
        <el-table-column align="center" label="昵称" prop="nickname"></el-table-column>
        <el-table-column align="center" label="邮箱" prop="email"></el-table-column>
        <el-table-column align="center" label="电话" prop="mobile"></el-table-column>
        <el-table-column align="center" label="创建时间" style="font-size: 5px">
            <template #default="scope">
                <span style="font-size: 10px">{{ scope.row.createTime }}</span>
            </template>
        </el-table-column>
        <el-table-column align="center" label="状态">
            <template #default="scope">
                <el-switch v-model="scope.row.userRole.enable" :loading="scope.row.loading" @change="changeAdminStatus(scope.row)" />
            </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="90px">
            <template #default="scope">
                <el-link type="primary" @click="editAdmin(scope.row)">编辑</el-link>&nbsp;
                <el-popconfirm title="确定删除这个角色吗?" confirm-button-text="确定" cancel-button-text="取消" @confirm="deleteData(scope.row.id)">
                    <template #reference>
                        <el-link type="primary">删除</el-link>
                    </template>
                </el-popconfirm>
            </template>
        </el-table-column>
    </el-table>
    <PageManage v-model="adminPage.page" :total="adminPage.total" load-init @reload="reload" />

    <el-dialog v-model="adminPage.showDialog" title="自定义管理员" width="500px">
        <el-form ref="formRef" label-width="80px" :model="adminPage.form" :rules="adminPage.rules">
            <el-form-item label="角色" prop="roleId">
                <el-select v-model="adminPage.form.roleId" placeholder="请选择" style="width: 90%" clearable>
                    <el-option v-for="role in adminPage.roles" :key="role.id" :label="role.name" :value="role.id" />
                </el-select>
                <el-link title="刷新" :icon="Refresh" @click="reloadRoles" />
            </el-form-item>
            <el-form-item label="昵称" prop="nickname">
                <el-input v-model="adminPage.form.nickname" :clearable="true" maxlength="20" />
            </el-form-item>
            <el-form-item :label="selectedUser.selected ? '用户' : '用户名'" prop="username">
                <el-autocomplete
                    v-model="adminPage.form.username"
                    :fetch-suggestions="queryUserByKeywords"
                    :teleported="false"
                    hide-loading
                    :trigger-on-focus="false"
                    :debounce="850"
                    clearable
                    :maxlength="16"
                    style="width: 100%"
                    @clear="clearSelectUser"
                    @select="selectUser"
                    @input="usernameChange"
                >
                    <template #default="{ item }">
                        <div>{{ item.username || item.mobile || item.email || item.userId }}</div>
                    </template>
                </el-autocomplete>
                <div class="select-user-tips">
                    <el-descriptions v-show="selectedUser.selected" :column="2" size="small" border>
                        <el-descriptions-item>
                            <template #label>用户</template>
                            <el-avatar :src="selectedUser.avatar" :icon="User" />
                            <br />{{ selectedUser.username }} <br />{{ selectedUser.nickname }}
                        </el-descriptions-item>
                        <el-descriptions-item v-if="selectedUser.mobile || selectedUser.email">
                            <template #label>联系</template>
                            {{ selectedUser.mobile }}
                            <br />{{ selectedUser.email }}
                        </el-descriptions-item>
                    </el-descriptions>
                    <span v-show="!selectedUser.selected" class="new-user">当前为新用户</span>
                    <p>亦可根据用户名、手机号或邮箱匹配,选择相应的用户账号</p>
                </div>
            </el-form-item>
            <el-form-item v-if="!selectedUser.selected" label="密码" prop="password">
                <el-input v-model="adminPage.form.password" type="password" show-password autocomplete="new-password" clearable maxlength="20" />
            </el-form-item>
            <el-form-item v-if="!selectedUser.selected" label="确认密码" prop="confirmPassword">
                <el-input v-model="adminPage.form.confirmPassword" type="password" show-password clearable maxlength="20" />
            </el-form-item>
            <el-form-item label="邮箱" prop="email" autocomplete="new-email">
                <el-input v-model="adminPage.form.email" clearable maxlength="64" />
            </el-form-item>
            <el-form-item label="联系电话" prop="mobile" autocomplete="new-mobile">
                <el-input v-if="!selectedUser.selected" v-model="adminPage.form.mobile" clearable maxlength="20" />
                <template v-else>{{ selectedUser.mobile }}</template>
            </el-form-item>
        </el-form>
        <template #footer>
            <span>
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="saveData">保存</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import PageManage from '@/components/pageManage/PageManage.vue'
import { markRaw, onMounted, reactive, ref, watch, computed } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { Refresh, User } from '@element-plus/icons-vue'
import { deleteShopAdmin, enableDisableShopCustomAdmin, getAdminPage, getAdminRegisterDataById, getAvailableUser, getRolePage, newShopCustomAdmin, updateShopCustomAdmin } from '@/apis/mall/permission'
onMounted(() => {
    //console.log('1231', 1231)
})
const defaultForm = {
    roleId: null,
    userId: null,
    nickname: null,
    username: null,
    password: null,
    confirmPassword: null,
    mobile: null,
    email: null,
}
const defaultSelectedUser = {
    selected: false,
    userId: null,
    username: null,
    mobile: null,
    email: null,
    avatar: null,
    nickname: null,
}
const namePattern = /^[a-zA-Z0-9!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]{4,16}$/
const passwordPattern = '^(?![a-zA-Z]+$)(?!\d+$)(?![^\da-zA-Z\s]+$).{1,9}$'
const formRef = ref<FormInstance>(null)
const selectedUser = ref({ ...defaultSelectedUser })
const autocompleteDisabled = ref(false)
const adminPage = reactive({
    queryUsers: [],
    selectedId: null,
    form: { ...defaultForm },
    rules: {
        roleId: [{ required: true, message: '请选择用户角色', trigger: 'change' }],
        nickname: [
            { required: true, message: '请输入用户昵称' },
            { min: 1, max: 20, message: '昵称限制在1-20个字符之间' },
        ],
        username: [
            { required: true, message: '请输入用户名或选择对应的用户', trigger: 'blur' },
            {
                validator(rule, value, callback) {
                    if (selectedUser.value.selected) {
                        callback()
                    }
                    if (!value) {
                        callback(new Error('请输入用户名或选择对应的用户'))
                    }
                    if (value.length > 16 || value.length < 4) {
                        callback(new Error('用户名长度限定在4～16个字符'))
                    }
                    if (!namePattern.test(value)) {
                        callback('支持输入数字、字母、字符，长度：4~16')
                    }
                    callback()
                },
            },
        ],
        userId: [{ required: true, message: '请输入用户名或选择对应的用户', trigger: 'blur' }],
        password: [
            { required: true, message: '请输入密码' },
            { min: 6, max: 20, message: '长度限制在6-20个字符之间' },
            { pattern: passwordPattern, message: '密码需要在大写字母、小写字母、数字、字符之间任选其二' },
        ],
        confirmPassword: [
            { required: true, message: '请输入确认密码' },
            { min: 6, max: 20, message: '长度限制在6-20个字符之间' },
            { pattern: passwordPattern, message: '密码需要在大写字母、小写字母、数字、特殊字符之间任选其二' },
            {
                validator: (rule, value, callback) => {
                    if (adminPage.form.confirmPassword !== adminPage.form.password) {
                        callback(new Error('确认密码和密码不一致'))
                    }
                    callback()
                },
                trigger: 'blur',
            },
        ],
        email: [
            { required: true, message: '请输入用户邮箱地址' },
            { type: 'email', message: '请输入正确的邮箱地址' },
        ],
        mobile: [
            { required: true, message: '请输入用户手机号', trigger: 'blur' },
            { pattern: '((?:0|86|\\+86)?1[3-9]\\d{9})|((010|02\\d|0[3-9]\\d{2})-?(\\d{6,8}))', message: '请输入正确的手机号' },
        ],
    } as FormRules,
    roles: [],
    showDialog: false,
    records: [],
    total: 0,
    page: { size: 10, current: 1 },
})
watch(
    () => selectedUser.value.selected,
    (value) => {
        adminPage.rules.mobile[0].required = !value
    },
)
const newAdmin = async () => {
    await reloadRoles()
    adminPage.showDialog = true
    adminPage.selectedId = null
    adminPage.form = { ...defaultForm }
    selectedUser.value = { ...defaultSelectedUser }
}
const usernameChange = () => {
    selectedUser.value.selected && clearSelectUser()
}
const reload = () => {
    getAdminPage({ ...adminPage.page }).then((response) => {
        const data = response.data
        adminPage.records = data.records
        adminPage.total = data.total
    })
}
const hideDialogAndReload = () => {
    adminPage.showDialog = false
    reload()
}
const saveData = () => {
    formRef.value.validate(async (valid) => {
        if (!valid) {
            return
        }
        const selectedId = adminPage.selectedId
        const form = markRaw(adminPage.form)
        if (selectedUser.value.selected) {
            form.username = null
        }
        if (!selectedId) {
            const { code, data, msg } = await newShopCustomAdmin(form)
            if (code !== 200) {
                ElMessage.error(msg ? msg : '保存失败')
                return
            }
            ElMessage.success('已保存')
            hideDialogAndReload()
            return
        }
        const { code, data, msg } = await updateShopCustomAdmin(selectedId, form)
        if (code !== 200) {
            ElMessage.error(msg ? msg : '保存失败')
            return
        }
        ElMessage.success('已保存')
        hideDialogAndReload()
    })
}
const queryUserByKeywords = async (keywords: string, cb: (arg: any) => void) => {
    if (!keywords) {
        autocompleteDisabled.value = true
        cb([])
        return
    }
    const { code, data } = await getAvailableUser(keywords)
    autocompleteDisabled.value = !data.records.length
    cb(data.records)
}
const selectUser = (data) => {
    if (!data) {
        selectedUser.value.selected = false
        return
    }
    const { userId, username, mobile, email, avatar, nickname, id } = data
    selectedUser.value = { selected: true, userId: id, username, mobile, email, avatar, nickname }
    if (!adminPage.form.username) {
        adminPage.form.username = username || nickname || mobile || email
    }
    adminPage.form.userId = userId
}
const clearSelectUser = () => {
    adminPage.form.userId = null
    adminPage.form.username = null
    selectedUser.value = { ...defaultSelectedUser }
}
const reloadRoles = () => {
    getRolePage({
        current: 1,
        size: 200,
    }).then((response) => {
        adminPage.roles = response.data.records
    })
}
const editAdmin = (row) => {
    const userId = row.userRole.user.id
    getAdminRegisterDataById(userId).then((response) => {
        const data = response.data
        if (!data) {
            return
        }
        selectUser({ ...row, ...data })
        adminPage.selectedId = row.id
        adminPage.form = {
            roleId: row.userRole.role.id,
            userId: userId,
            nickname: row.nickname,
            username: data.username || data.nickname,
            password: null,
            confirmPassword: null,
            mobile: row.mobile,
            email: row.email,
        }
        adminPage.showDialog = true
    })
}
const changeAdminStatus = (row) => {
    row.loading = true
    enableDisableShopCustomAdmin(row.id, row.userRole.enable)
        .then(() => {
            ElMessage.success('已更改')
            row.loading = false
        })
        .catch(() => {
            row.loading = false
        })
}
const deleteData = (dataId: string) => {
    console.log(1)
    deleteShopAdmin(dataId).then((res) => {
        if (res.code !== 200) {
            return
        }

        ElMessage.success('已删除')
        hideDialogAndReload()
    })
}
const handleClose = () => {
    formRef.value.clearValidate()
    adminPage.showDialog = false
}
</script>

<style scoped lang="scss">
.select-user-tips {
    font-size: 4px !important;
    color: $rows-text-color-grey;
}

.select-user-tips .new-user {
    color: #b67c03;
}
</style>
