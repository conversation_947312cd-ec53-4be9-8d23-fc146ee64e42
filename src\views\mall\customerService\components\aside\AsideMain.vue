<template>
    <el-menu :collapse="collapse" class="customer-service-users" :default-active="active">
        <el-menu-item
            v-for="messageUser in props.messageUsers"
            :key="messageUser.chatWithShopInfo.shopId"
            :index="messageUser.chatWithShopInfo.shopId"
            style="padding: 0"
            @click="onChange(messageUser)"
        >
            <el-badge is-dot :hidden="messageUser.lastMessage?.read" :type="messageUser.lastMessage?.handled ? 'warning' : 'danger'">
                <el-avatar style="background: transparent" :size="50" :src="global.getUserAvatar(messageUser.chatWithShopInfo.shopLogo)" />
            </el-badge>
            <template #title>
                <div class="user-desc">
                    <div class="user-msg user-nickname">
                        <div class="nickname">
                            {{ global.getUserNickname(messageUser.chatWithShopInfo.shopId, messageUser.chatWithShopInfo.shopName) }}
                        </div>
                        <div class="last-time">
                            {{ renderTime(messageUser?.lastMessage?.sendTime) }}
                        </div>
                    </div>
                    <div class="user-msg last-message">
                        {{ renderMessage(messageUser.lastMessage) }}
                    </div>
                </div>
            </template>
        </el-menu-item>
    </el-menu>
</template>
<script setup lang="ts">
import { inject, onMounted, PropType, ref, unref, watch } from 'vue'
import { useRoute } from 'vue-router'
import DateUtil from '@/utils/date'
import { MessageType, MessageUser, Message } from '../../types'

const props = defineProps({
    messageUsers: {
        type: Array as PropType<Array<MessageUser>>,
        default: () => [],
    },
})
const collapse = ref(false)
const global = inject('global')
const emits = defineEmits(['change'])

const onChange = (selectUser: MessageUser) => {
    if (selectUser.lastMessage) selectUser.lastMessage.read = true
    emits('change', { ...selectUser })
}
const route = useRoute()
onMounted(() => {
    initSelectUser()
})

// 监听用户列表变化，当列表加载完成后尝试选择用户
watch(
    () => props.messageUsers,
    (newUsers) => {
        if (newUsers.length > 0 && route.query.id) {
            initSelectUser()
        }
    },
    { deep: true },
)
const active = ref('')
const initSelectUser = () => {
    if (route.query.id) {
        active.value = route.query.id as string

        if (props.messageUsers.length > 0) {
            const selectUser = props.messageUsers.find((item) => item.chatWithShopInfo.shopId === route.query.id)
            if (selectUser) {
                // 自动选中该用户，触发聊天窗口打开
                emits('change', selectUser)
                console.log('已自动选中用户并打开聊天窗口:', selectUser.chatWithShopInfo.shopName)
            } else {
                console.warn('未找到对应的用户，shopId:', route.query.id)
                // 如果没找到对应用户，但有其他用户，可以选择第一个用户
                if (props.messageUsers.length > 0) {
                    emits('change', props.messageUsers[0])
                    active.value = props.messageUsers[0].chatWithShopInfo.shopId
                }
            }
        } else {
            // 用户列表还未加载完成，稍后重试
            setTimeout(initSelectUser, 100)
        }
    } else if (props.messageUsers.length > 0) {
        // 如果没有指定用户ID，默认选择第一个用户
        emits('change', props.messageUsers[0])
        active.value = props.messageUsers[0].chatWithShopInfo.shopId
    }
}

const renderTime = (time: any) => {
    if (!time) return ''
    const lastTime = new Date(+time)
    const dateUtil = new DateUtil(lastTime)
    const isToday = new Date().getDay() === lastTime.getDay()
    return isToday ? dateUtil.getH() + ':' + dateUtil.getMin() : dateUtil.getYMD()
}

const renderMessage = (message: Message) => {
    if (!message) return ''
    switch (message.messageType) {
        case MessageType.PRODUCT:
            return '[商品]'
        case MessageType.IMAGE:
            return '[图片]'
        case MessageType.ORDER:
            return '[订单]'
        case MessageType.ORDER_WARNING:
            return '[订单预警]'
        default:
            return message.message
    }
}
</script>
<style scoped lang="scss">
.el-menu {
    border-right: none;
    padding: $rows-spacing-row-sm;
}
.el-menu .el-menu-item {
    margin-top: 10px;
    margin-bottom: 10px;
    color: $rows-text-color;
    padding: 0;
}

.el-menu .el-menu-item.is-active {
    background-color: $rows-bg-color-hover;
}

.el-menu .el-menu-item .el-badge {
    line-height: 100%;
    margin: 0;
    padding: 0;
}
.el-menu .el-menu-item .user-desc {
    height: 100%;
    flex: 1;
    padding: 3px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.user-desc .user-msg {
    line-height: 1;
    font-size: 12px;
    width: 129px;
}
.user-desc .user-msg.user-nickname {
    font-size: 12px;
    display: flex;
    position: relative;
    justify-content: space-between;
}
.user-desc .user-msg.user-nickname .nickname {
    width: 79px;
    @include utils-ellipsis(1);
}
.user-desc .user-msg.user-nickname .last-time {
    color: $rows-text-color-grey;
    position: absolute;
    right: 2px;
    top: 50%;
    transform: scale(0.8) translateY(-50%);
}

.user-desc .user-msg.last-message {
    color: $rows-text-color-grey;
    padding-top: 10px;
    @include utils-ellipsis(1);
}
</style>
<style>
.customer-service-users.el-menu .el-menu-item .el-badge .el-badge__content {
    height: 10px;
    width: 10px;
}
</style>
